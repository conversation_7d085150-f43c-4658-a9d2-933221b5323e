{"name": "digital-twin-app", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "npx vite", "build": "npx vite build", "lint": "npx eslint .", "preview": "npx vite preview"}, "dependencies": {"@react-three/drei": "^9.88.13", "@react-three/fiber": "^8.15.11", "@types/arcgis-js-api": "^4.27.2", "lucide-react": "^0.344.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-dropzone": "^14.2.3", "recharts": "^2.8.0", "three": "^0.158.0"}, "devDependencies": {"@eslint/js": "^9.9.1", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@types/three": "^0.158.3", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^5.4.2"}}